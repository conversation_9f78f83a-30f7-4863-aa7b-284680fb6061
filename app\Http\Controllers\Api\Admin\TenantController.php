<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Tenant\TenantRepositoryInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Enums\TenantStatus;
use Illuminate\Support\Facades\Hash;
use App\Repositories\User\UserRepositoryInterface;
use Illuminate\Support\Facades\DB;

class TenantController extends Controller
{
    private $tenantRepository;
    private $userRepository;

    public function __construct(
        TenantRepositoryInterface $tenantRepository,
        UserRepositoryInterface $userRepository,
    )
    {
        $this->tenantRepository = $tenantRepository;
        $this->userRepository = $userRepository;
    }
    
    public function getAllTenants(Request $request)
    {
        $dataSearch = $request->all();
        $result = $this->tenantRepository->getAllTenants($dataSearch);
        if (isset($result)) {
            
            return response()->json([
                'status' => 'success',
                'counts' => $result['counts'],
                'tenants' => $result['tenants'],
            ], 200);
        } else {

            return response()->json(['status' => 'not_data'], 200);
        }
    }

    public function storeTenant(Request $request)
    {
        try {
            DB::beginTransaction();
            // Kiểm tra domain_name trùng lặp
            $existingTenant = $this->tenantRepository->findByDomainName($request->domain_name);

            if ($existingTenant) {
                return response()->json([
                    'status' => 'DUPLICATE_DOMAIN_NAME',
                    'message' => 'Domain name already exists',
                ]);
            }

            $data_tenant = [
                'name' => $request->name,
                'domain_name' => $request->domain_name,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'is_active' => TenantStatus::ACTIVE->value,
                'create_by' => Auth::id(),
            ];

            $tenant = $this->tenantRepository->create($data_tenant);

            $data_user = [
                'account_name' => $request->account_name,
                'full_name' => $request->full_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'tenant_id' => $tenant->id,
            ];
            
            $user = $this->userRepository->create($data_user);
            $user->assignRoles('Quản trị viên');

            DB::commit();

            return response()->json([
                'status' => 'success',
                'data' => $tenant,
            ], 201);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create tenant',
                'data' => $th->getMessage(),
            ], 500);
        }
    }

    public function updateTenant(Request $request, $id)
    {
        try {
            // Validation
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'domain_name' => 'required|string|max:255|unique:tenants,domain_name,' . $id,
                'start_date' => 'required|date',
                'end_date' => 'nullable|date|after:start_date',
                'is_active' => 'required|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $tenant = $this->tenantRepository->find($id);
            if (!$tenant) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Tenant not found',
                ], 404);
            }

            $data_tenant = [
                'name' => $request->name,
                'domain_name' => $request->domain_name,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'is_active' => $request->is_active,
            ];

            $updated_tenant = $this->tenantRepository->update($id, $data_tenant);
            
            return response()->json([
                'status' => 'success',
                'data' => $updated_tenant,
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update tenant',
                'data' => $th->getMessage(),
            ], 500);
        }
    }

    public function deleteTenant($id)
    {
        try {
            $tenant = $this->tenantRepository->find($id);
            if (!$tenant) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Tenant not found',
                ], 404);
            }

            $this->tenantRepository->delete($id);
            
            return response()->json([
                'status' => 'success',
                'message' => 'Tenant deleted successfully',
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete tenant',
                'data' => $th->getMessage(),
            ], 500);
        }
    }

    public function show($id)
    {
        try {
            $tenant = $this->tenantRepository->find($id, ['*']);
            if (!$tenant) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Tenant not found',
                ], 404);
            }

            $tenant->load('createBy:id,name');
            
            return response()->json([
                'status' => 'success',
                'data' => $tenant,
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get tenant',
                'data' => $th->getMessage(),
            ], 500);
        }
    }
}
