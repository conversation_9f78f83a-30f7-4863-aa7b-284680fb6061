<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Tenant;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class IdentifyTenant
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Lấy tên miền từ request hiện tại
        $host = $request->getHost();
        $cacheKey = "tenant_domain_{$host}";
        $tenant = null;
        if (Cache::has($cacheKey)) {
            $tenant = Cache::get($cacheKey);
        } else {
            // Lấy ngày hiện tại
            $current_date = Carbon::now()->format('Y-m-d');

            // Tìm đơn vị theo domain VÀ kiểm tra điều kiện hiệu lực trong cùng một query
            $tenantFromDb = Tenant::where('domain_name', $host)
                ->where(function ($query) use ($current_date) {
                    // Điều kiện 1: start_date phải nhỏ hơn hoặc bằng ngày hiện tại
                    $query->where('start_date', '<=', $current_date);
                    // Điều kiện 2: Phải thỏa mãn MỘT TRONG HAI điều kiện con sau
                    $query->where(function ($subQuery) use ($current_date) {
                        // Hoặc end_date là NULL (vô thời hạn)
                        $subQuery->whereNull('end_date')
                            // Hoặc end_date lớn hơn hoặc bằng ngày hiện tại
                            ->orWhere('end_date', '>=', $current_date);
                    });
            })
            // Tìm bản ghi đầu tiên, nếu không có bản ghi nào thỏa mãn tất cả điều kiện trên,
            // hệ thống sẽ tự động trả về lỗi 404 Not Found.
            ->first();

            if ($tenantFromDb) {
                Cache::put($cacheKey, $tenantFromDb, 3600); // Lưu trong 1 giờ
            }

            // Gán kết quả từ database cho biến tenant
            $tenant = $tenantFromDb;
        }
        
        // Nếu không tìm thấy đơn vị, báo lỗi 404
        if (!$tenant) {
            abort(404, 'Đơn vị không tồn tại hoặc tên miền không hợp lệ hoặc đã hết hạn');
        }

        // Nếu tìm thấy, lưu thông tin đơn vị vào service container
        // để toàn bộ ứng dụng có thể truy cập dễ dàng.
        app()->singleton('tenant', function () use ($tenant) {
            return $tenant;
        });

        // Tiếp tục request
        return $next($request);
    }
}
