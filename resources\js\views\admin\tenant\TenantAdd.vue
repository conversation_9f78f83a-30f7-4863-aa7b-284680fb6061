<template>
    <CRow>
        <CCol :xs="12">
            <CCardBody>
                <CForm @submit.prevent="handleSubmit" class="row gx-3 gy-3 align-items-center">
                    <BTabs no-body content-class="mt-3">
                        <BTab :title="$t('tenant.tab.info')">
                            <CCol :md="12" class="mb-3">
                                <CFormLabel for="name">{{ $t('tenant.name') }} <span class="text-danger">*</span>
                                </CFormLabel>
                                <CFormInput
                                    v-model="state.form.name"
                                    type="text"
                                    :placeholder="$t('tenant.name_placeholder')"
                                    required
                                />
                            </CCol>
                            <CCol :md="12" class="mb-3">
                                <CFormLabel for="domain_name">{{ $t('tenant.domain_name') }} <span class="text-danger">*</span></CFormLabel>
                                <CFormInput
                                    v-model="state.form.domain_name"
                                    type="text"
                                    :placeholder="$t('tenant.domain_name_placeholder')"
                                    required
                                />
                            </CCol>
                            <CRow class="mb-3">
                                <CCol :md="6">
                                    <CFormLabel for="start_date">{{ $t('tenant.start_date') }} <span class="text-danger">*</span></CFormLabel>
                                    <CFormInput
                                        v-model="state.form.start_date"
                                        type="date"
                                        required
                                    />
                                </CCol>
                                <CCol :md="6">
                                    <CFormLabel for="end_date">{{ $t('tenant.end_date') }}</CFormLabel>
                                    <CFormInput
                                        v-model="state.form.end_date"
                                        type="date"
                                    />
                                </CCol>
                            </CRow>
                        </BTab>
                        <BTab :title="$t('tenant.tab.account')">
                            <CCol :md="12" class="mb-3">
                                <CFormLabel for="account_name">{{ $t('tenant.account_name') }} <span class="text-danger">*</span>
                                </CFormLabel>
                                <CFormInput
                                    v-model="state.form.account_name"
                                    type="text"
                                    :placeholder="$t('tenant.account_name_placeholder')"
                                    required
                                />
                            </CCol>
                            <CCol :md="12" class="mb-3">
                                <CFormLabel for="full_name">{{ $t('tenant.full_name') }} <span class="text-danger">*</span></CFormLabel>
                                <CFormInput
                                    v-model="state.form.full_name"
                                    type="text"
                                    :placeholder="$t('tenant.full_name_placeholder')"
                                    required
                                />
                            </CCol>
                            <CRow class="mb-3">
                                <CCol :md="6">
                                    <CFormLabel for="email">{{ $t('tenant.email') }} <span class="text-danger">*</span></CFormLabel>
                                    <CFormInput
                                        v-model="state.form.email"
                                        type="email"
                                        :placeholder="$t('tenant.email_placeholder')"
                                        required
                                    />
                                </CCol>
                                <CCol :md="6">
                                    <CFormLabel for="password">{{ $t('tenant.password') }} <span class="text-danger">*</span></CFormLabel>
                                    <CInputGroup>
                                        <CFormInput
                                            :type="state.showPassword ? 'text' : 'password'"
                                            :placeholder="$t('tenant.password_placeholder')"
                                            autocomplete="password"
                                            v-model="state.form.password"
                                            required
                                        />
                                        <span class="password-toggle-icon material-symbols-outlined" @click="state.showPassword = !state.showPassword">
                                            {{ state.showPassword ? 'visibility' : 'visibility_off' }}
                                        </span>
                                    </CInputGroup>
                                </CCol>
                            </CRow>
                        </BTab>
                    </BTabs>
                    
                    <!-- Submit Button -->
                    <CCardFooter>
                        <div class="d-flex justify-content-end">
                            <CButton 
                                type="button"
                                class="btn btn-light border m-1"
                                @click="handleCancel"
                                :disabled="setIsLoading"
                            >
                                <span class="text-uppercase">
                                    {{ $t('tenant.close') }}
                                </span>
                            </CButton>
                            <CButton 
                                type="submit"
                                class="btn btn-primary m-1"
                                :disabled="setIsLoading"
                            >
                                <CSpinner v-if="setIsLoading" size="sm" class="me-2" />
                                {{ $t('tenant.save_update') }}
                            </CButton>
                        </div>
                    </CCardFooter>
            </CForm>
            </CCardBody>
        </CCol>
    </CRow>
</template>

<script lang="ts">
import { defineComponent, reactive, computed } from 'vue'
import { useToast } from 'vue-toast-notification'
import { useI18n } from "vue-i18n";
import useTenants from '@/composables/tenant';

export default defineComponent({
    name: "TenantAdd",

    props: {
        editData: {
            type: Object,
            default: null
        }
    },

    emits: ['close-modal', 'tenant-added', 'tenant-updated'],

    setup(props, { emit }) {
        const $toast = useToast()
        const { t } = useI18n()
        
        const state = reactive({
            showPassword: false,
            form: {
                name: '',
                domain_name: '',
                start_date: '',
                end_date: '',
                account_name: 'Admin',
                full_name: 'Quản trị viên',
                email: '',
                password: '',
            }
        })

        const { setIsLoading, storeTenant, updateTenant } = useTenants()

        const isEditMode = computed(() => !!props.editData)

        // Initialize form data if editing
        if (props.editData) {
            state.form.name = props.editData.name || ''
            state.form.domain_name = props.editData.domain_name || ''
            state.form.start_date = props.editData.start_date || ''
            state.form.end_date = props.editData.end_date || ''
            state.form.account_name = props.editData.account_name || ''
            state.form.full_name = props.editData.full_name || ''
            state.form.email = props.editData.email || ''
            state.form.password = props.editData.password || ''
        }

        const handleSubmit = async () => {
            const formData = {
                name: state.form.name.trim(),
                domain_name: state.form.domain_name.trim(),
                start_date: state.form.start_date,
                end_date: state.form.end_date || null,
                account_name: state.form.account_name,
                full_name: state.form.full_name,
                email: state.form.email,
                password: state.form.password,
            }

            let response
            if (isEditMode.value) {
                response = await updateTenant(props.editData.id, formData)
                if (response?.status === 'success') {
                    $toast.open({
                        message: t('toast.status.ACTION_SUCCESS'),
                        type: "success",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    })
                    emit('tenant-updated')
                    emit('close-modal')
                } 
            } else {
                response = await storeTenant(formData)
                if (response?.status === 'success') {
                    $toast.open({
                        message: t('toast.status.ACTION_SUCCESS'),
                        type: "success",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    })
                    emit('tenant-added')
                    emit('close-modal')
                } else if (response?.status === 'DUPLICATE_DOMAIN_NAME') {
                    $toast.open({
                        message: t('toast.status.DUPLICATE_DOMAIN_NAME'),
                        type: "error",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    })
                }
            }
        }

        const handleCancel = () => {
            emit('close-modal')
        }

        return {
            state,
            setIsLoading,
            isEditMode,
            handleSubmit,
            handleCancel
        }
    }
});
</script>

<style scoped>
.password-toggle-icon {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    cursor: pointer;
    color: #757575;
    background: none;
    border: none;
    outline: none;
    user-select: none;
    z-index: 10;
}
</style>
